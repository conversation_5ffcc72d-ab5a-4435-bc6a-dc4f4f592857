#!/usr/bin/env python3
"""
Test script for the Service CRUD API
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000"

def get_admin_token():
    """Get JWT token for admin user"""
    print("Getting admin token...")
    
    data = {
        "phone": "+1234567890",  # Replace with your admin phone
        "password": "admin123"   # Replace with your admin password
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=data)
    if response.status_code == 200:
        token = response.json().get('access')
        print("✅ Admin token obtained")
        return token
    else:
        print(f"❌ Failed to get admin token: {response.status_code}")
        print(response.json())
        return None

def test_create_service_single_tool(token):
    """Test creating a service with single tool"""
    print("\n🧪 Testing: Create service with single tool...")
    
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "name": "SMS Service",
        "isActive": True,
        "toolName": "twilio"
    }
    
    response = requests.post(f"{BASE_URL}/api/services/", json=data, headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 201:
        print("✅ Service created successfully")
        return response.json()['id']
    else:
        print("❌ Failed to create service")
        return None

def test_create_service_multiple_tools(token):
    """Test creating a service with multiple tools"""
    print("\n🧪 Testing: Create service with multiple tools...")
    
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "name": "Payment Service",
        "isActive": True,
        "toolName": ["stripe", "paypal", "square"]
    }
    
    response = requests.post(f"{BASE_URL}/api/services/", json=data, headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 201:
        print("✅ Service created successfully")
        return response.json()['id']
    else:
        print("❌ Failed to create service")
        return None

def test_list_services(token):
    """Test listing all services"""
    print("\n🧪 Testing: List all services...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/services/", headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ Services listed successfully")
    else:
        print("❌ Failed to list services")

def test_filter_active_services(token):
    """Test filtering active services"""
    print("\n🧪 Testing: Filter active services...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/services/?isActive=true", headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ Active services filtered successfully")
    else:
        print("❌ Failed to filter active services")

def test_search_by_tool(token):
    """Test searching services by tool"""
    print("\n🧪 Testing: Search services by tool...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/services/search-by-tool/?tool=stripe", headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ Services searched by tool successfully")
    else:
        print("❌ Failed to search services by tool")

def test_update_service(token, service_id):
    """Test updating a service"""
    if not service_id:
        print("\n⏭️  Skipping update test - no service ID")
        return
    
    print(f"\n🧪 Testing: Update service {service_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    data = {
        "name": "Updated Payment Service",
        "isActive": True,
        "toolName": ["stripe", "paypal"]  # Removed square
    }
    
    response = requests.put(f"{BASE_URL}/api/services/{service_id}/", json=data, headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ Service updated successfully")
    else:
        print("❌ Failed to update service")

def test_deactivate_service(token, service_id):
    """Test deactivating a service"""
    if not service_id:
        print("\n⏭️  Skipping deactivate test - no service ID")
        return
    
    print(f"\n🧪 Testing: Deactivate service {service_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.patch(f"{BASE_URL}/api/services/{service_id}/deactivate/", headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ Service deactivated successfully")
    else:
        print("❌ Failed to deactivate service")

def test_soft_delete_service(token, service_id):
    """Test soft deleting a service"""
    if not service_id:
        print("\n⏭️  Skipping soft delete test - no service ID")
        return
    
    print(f"\n🧪 Testing: Soft delete service {service_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.delete(f"{BASE_URL}/api/services/{service_id}/", headers=headers)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200:
        print("✅ Service soft deleted successfully")
    else:
        print("❌ Failed to soft delete service")

def test_unauthorized_access():
    """Test accessing API without admin token"""
    print("\n🧪 Testing: Unauthorized access...")
    
    response = requests.get(f"{BASE_URL}/api/services/")
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 401:
        print("✅ Unauthorized access properly blocked")
    else:
        print("❌ Unauthorized access not properly blocked")

def main():
    """Run all tests"""
    print("🚀 Starting Service API Tests")
    print("=" * 50)
    
    # Test unauthorized access first
    test_unauthorized_access()
    
    # Get admin token
    token = get_admin_token()
    if not token:
        print("❌ Cannot proceed without admin token")
        return
    
    # Test CRUD operations
    service_id_1 = test_create_service_single_tool(token)
    service_id_2 = test_create_service_multiple_tools(token)
    
    test_list_services(token)
    test_filter_active_services(token)
    test_search_by_tool(token)
    
    # Test updates and deletions
    test_update_service(token, service_id_2)
    test_deactivate_service(token, service_id_1)
    test_soft_delete_service(token, service_id_2)
    
    # Final list to see results
    print("\n🧪 Final service list:")
    test_list_services(token)
    
    print("\n" + "=" * 50)
    print("🎉 Service API Tests Completed!")

if __name__ == "__main__":
    main()
