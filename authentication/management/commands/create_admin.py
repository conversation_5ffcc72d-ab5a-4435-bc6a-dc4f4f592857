from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from authentication.models import Admin

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a default admin user'

    def add_arguments(self, parser):
        parser.add_argument(
            '--phone',
            type=str,
            default='+1234567890',
            help='Phone number for the admin user (default: +1234567890)'
        )
        parser.add_argument(
            '--password',
            type=str,
            default='admin123',
            help='Password for the admin user (default: admin123)'
        )
        parser.add_argument(
            '--first-name',
            type=str,
            default='Admin',
            help='First name for the admin user (default: Admin)'
        )
        parser.add_argument(
            '--last-name',
            type=str,
            default='User',
            help='Last name for the admin user (default: User)'
        )

    def handle(self, *args, **options):
        phone = options['phone']
        password = options['password']
        first_name = options['first_name']
        last_name = options['last_name']

        # Check if admin user already exists
        if User.objects.filter(phone=phone).exists():
            self.stdout.write(
                self.style.WARNING(f'Admin user with phone {phone} already exists.')
            )
            return

        # Create admin user
        try:
            admin_user = User.objects.create_superuser(
                phone=phone,
                password=password,
                firstName=first_name,
                lastName=last_name
            )
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created admin user:\n'
                    f'Phone: {phone}\n'
                    f'Password: {password}\n'
                    f'Name: {first_name} {last_name}'
                )
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating admin user: {str(e)}')
            )
