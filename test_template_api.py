#!/usr/bin/env python3
"""
Test script for Template CRUD API endpoints
Tests all functionality including file uploads and relationships
"""

import requests
import json
import os
import tempfile
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

# Test credentials (Admin user)
ADMIN_CREDENTIALS = {
    "phone": "+1234567890",  # Replace with actual admin phone
    "password": "admin123"   # Replace with actual admin password
}

class APITester:
    def __init__(self):
        self.token = None
        self.headers = {}
        
    def authenticate(self):
        """Authenticate and get JWT token"""
        print("🔐 Authenticating...")
        
        response = requests.post(f"{BASE_URL}/auth/login/", json=ADMIN_CREDENTIALS)
        
        if response.status_code == 200:
            data = response.json()
            self.token = data.get('access')
            self.headers = {
                'Authorization': f'Bearer {self.token}',
                'Content-Type': 'application/json'
            }
            print("✅ Authentication successful")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    
    def get_services(self):
        """Get available services for testing"""
        print("\n📋 Getting available services...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f"{API_BASE}/services/", headers=headers)
        
        if response.status_code == 200:
            services = response.json().get('results', [])
            print(f"✅ Found {len(services)} services")
            return services
        else:
            print(f"❌ Failed to get services: {response.status_code}")
            return []
    
    def test_template_crud(self, service_id):
        """Test Template CRUD operations"""
        print("\n" + "="*60)
        print("🧪 TESTING TEMPLATE CRUD OPERATIONS")
        print("="*60)
        
        template_id = None
        
        try:
            # 1. CREATE Template
            print("\n1️⃣ Testing Template Creation...")
            template_id = self.create_template(service_id)
            if not template_id:
                return False
            
            # 2. READ Template
            print("\n2️⃣ Testing Template Retrieval...")
            if not self.get_template(template_id):
                return False
            
            # 3. UPDATE Template
            print("\n3️⃣ Testing Template Update...")
            if not self.update_template(template_id):
                return False
            
            # 4. LIST Templates
            print("\n4️⃣ Testing Template Listing...")
            if not self.list_templates():
                return False
            
            # 5. SEARCH Templates
            print("\n5️⃣ Testing Template Search...")
            if not self.search_templates():
                return False
            
            # 6. FILTER Templates
            print("\n6️⃣ Testing Template Filtering...")
            if not self.filter_templates(service_id):
                return False
            
            # 7. CUSTOM ACTIONS
            print("\n7️⃣ Testing Template Custom Actions...")
            if not self.test_template_actions(template_id):
                return False
            
            print("\n✅ All Template CRUD tests passed!")
            return True
            
        except Exception as e:
            print(f"\n❌ Template CRUD test failed: {str(e)}")
            return False
        
        finally:
            # Cleanup
            if template_id:
                self.cleanup_template(template_id)
    
    def create_template(self, service_id):
        """Test template creation with file upload"""
        print("📤 Creating template with files...")
        
        # Create temporary files
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(b"Sample PDF content for testing" * 100)
            file_path = temp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
            temp_video.write(b"Sample video content for testing" * 1000)
            video_path = temp_video.name
        
        try:
            data = {
                'title': 'Test Template',
                'description': 'A test template for API testing',
                'service': service_id,
                'isActive': True
            }
            
            files = {
                'file': ('test_template.pdf', open(file_path, 'rb'), 'application/pdf'),
                'demo_video': ('test_demo.mp4', open(video_path, 'rb'), 'video/mp4')
            }
            
            headers = {'Authorization': f'Bearer {self.token}'}
            response = requests.post(f"{API_BASE}/templates/", headers=headers, data=data, files=files)
            
            files['file'][1].close()
            files['demo_video'][1].close()
            
            if response.status_code == 201:
                template = response.json()
                print(f"✅ Template created: ID {template['id']}")
                return template['id']
            else:
                print(f"❌ Template creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        finally:
            os.unlink(file_path)
            os.unlink(video_path)
    
    def get_template(self, template_id):
        """Test template retrieval"""
        print(f"📥 Getting template {template_id}...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f"{API_BASE}/templates/{template_id}/", headers=headers)
        
        if response.status_code == 200:
            template = response.json()
            print(f"✅ Template retrieved: {template['title']}")
            return True
        else:
            print(f"❌ Template retrieval failed: {response.status_code}")
            return False
    
    def update_template(self, template_id):
        """Test template update"""
        print(f"📝 Updating template {template_id}...")
        
        data = {
            'title': 'Updated Test Template',
            'description': 'Updated description for testing'
        }
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.patch(f"{API_BASE}/templates/{template_id}/", headers=headers, data=data)
        
        if response.status_code == 200:
            template = response.json()
            print(f"✅ Template updated: {template['title']}")
            return True
        else:
            print(f"❌ Template update failed: {response.status_code}")
            return False
    
    def list_templates(self):
        """Test template listing"""
        print("📋 Listing templates...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.get(f"{API_BASE}/templates/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Templates listed: {data['count']} total")
            return True
        else:
            print(f"❌ Template listing failed: {response.status_code}")
            return False
    
    def search_templates(self):
        """Test template search"""
        print("🔍 Searching templates...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        params = {'search': 'test'}
        response = requests.get(f"{API_BASE}/templates/", headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Template search completed: {len(data['results'])} results")
            return True
        else:
            print(f"❌ Template search failed: {response.status_code}")
            return False
    
    def filter_templates(self, service_id):
        """Test template filtering"""
        print("🔽 Filtering templates...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        params = {'service': service_id, 'isActive': True}
        response = requests.get(f"{API_BASE}/templates/", headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Template filtering completed: {len(data['results'])} results")
            return True
        else:
            print(f"❌ Template filtering failed: {response.status_code}")
            return False
    
    def test_template_actions(self, template_id):
        """Test template custom actions"""
        print(f"⚡ Testing template actions for ID {template_id}...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        
        # Test deactivate
        response = requests.post(f"{API_BASE}/templates/{template_id}/deactivate/", headers=headers)
        if response.status_code != 200:
            print(f"❌ Template deactivate failed: {response.status_code}")
            return False
        
        # Test activate
        response = requests.post(f"{API_BASE}/templates/{template_id}/activate/", headers=headers)
        if response.status_code != 200:
            print(f"❌ Template activate failed: {response.status_code}")
            return False
        
        print("✅ Template actions completed")
        return True
    
    def cleanup_template(self, template_id):
        """Clean up test template"""
        print(f"🧹 Cleaning up template {template_id}...")
        
        headers = {'Authorization': f'Bearer {self.token}'}
        response = requests.delete(f"{API_BASE}/templates/{template_id}/", headers=headers)
        
        if response.status_code == 200:
            print("✅ Template cleaned up")
        else:
            print(f"⚠️ Template cleanup warning: {response.status_code}")

def main():
    """Main test function"""
    print("🧪 Template API Test Suite")
    print("="*60)
    
    tester = APITester()
    
    # Authenticate
    if not tester.authenticate():
        return
    
    # Get services for testing
    services = tester.get_services()
    if not services:
        print("❌ No services available for testing")
        return
    
    service_id = services[0]['id']
    print(f"📌 Using service: {services[0]['name']} (ID: {service_id})")
    
    # Run tests
    success = True
    
    # Test Template CRUD
    if not tester.test_template_crud(service_id):
        success = False
    
    # Final results
    print("\n" + "="*60)
    if success:
        print("🎉 ALL TESTS PASSED!")
    else:
        print("❌ SOME TESTS FAILED!")
    print("="*60)

if __name__ == "__main__":
    main()
