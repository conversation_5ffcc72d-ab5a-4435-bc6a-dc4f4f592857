#!/usr/bin/env python3
"""
Example script showing how to create Template via API
"""

import requests
import tempfile
import os

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

# Admin credentials (replace with your actual credentials)
ADMIN_CREDENTIALS = {
    "phone": "+1234567890",  # Replace with actual admin phone
    "password": "admin123"   # Replace with actual admin password
}

def authenticate():
    """Get JWT token"""
    print("🔐 Authenticating...")
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=ADMIN_CREDENTIALS)
    
    if response.status_code == 200:
        data = response.json()
        token = data.get('access')
        print("✅ Authentication successful")
        return token
    else:
        print(f"❌ Authentication failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def get_services(token):
    """Get available services"""
    print("\n" + "="*50)
    print("📋 GETTING AVAILABLE SERVICES")
    print("="*50)
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(f"{API_BASE}/services/", headers=headers)
    
    if response.status_code == 200:
        services = response.json().get('results', [])
        print(f"✅ Found {len(services)} services")
        for service in services:
            print(f"   - ID: {service['id']}, Name: {service['name']}")
        return services
    else:
        print(f"❌ Failed to get services: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def create_template_example(token, service_id):
    """Create a Template example"""
    print("\n" + "="*50)
    print("📄 CREATING TEMPLATE EXAMPLE")
    print("="*50)
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    # Create temporary files for demonstration
    print("📁 Creating temporary files...")
    
    # Create a temporary PowerPoint file
    with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_file:
        # Create a simple PowerPoint content
        pptx_content = b"""PK\x03\x04\x14\x00\x06\x00\x08\x00\x00\x00!\x00"""  # Basic PPTX header
        temp_file.write(pptx_content)
        temp_file.write(b"Sample PowerPoint Template Content" * 100)  # Add some content
        template_file_path = temp_file.name
    
    # Create a temporary video file
    with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_video:
        # Create a simple MP4 content (not a real video, just for demo)
        mp4_content = b"""ftypmp41\x00\x00\x00\x00mp41isom"""  # Basic MP4 header
        temp_video.write(mp4_content)
        temp_video.write(b"Sample video content for demo" * 1000)  # Add some content
        video_file_path = temp_video.name
    
    try:
        # Template data
        template_data = {
            'title': 'Professional Invoice Template',
            'description': 'A comprehensive PowerPoint template for creating professional invoices with modern design elements',
            'service': service_id,
            'isActive': True
        }
        
        # Prepare files
        files = {
            'file': ('invoice_template.pptx', open(template_file_path, 'rb'), 'application/vnd.openxmlformats-officedocument.presentationml.presentation'),
            'demo_video': ('invoice_demo.mp4', open(video_file_path, 'rb'), 'video/mp4')
        }
        
        print(f"📤 Sending template creation request...")
        print(f"   Title: {template_data['title']}")
        print(f"   Service ID: {template_data['service']}")
        print(f"   File: invoice_template.pptx")
        print(f"   Demo Video: invoice_demo.mp4")
        
        response = requests.post(
            f"{API_BASE}/templates/",
            headers=headers,
            data=template_data,
            files=files
        )
        
        # Close files
        files['file'][1].close()
        files['demo_video'][1].close()
        
        if response.status_code == 201:
            template = response.json()
            print("✅ Template created successfully!")
            print(f"   ID: {template['id']}")
            print(f"   Title: {template['title']}")
            print(f"   Service: {template['service_name']}")
            print(f"   File Size: {template['file_size_mb']} MB")
            print(f"   Video Size: {template['demo_video_size_mb']} MB")
            print(f"   File URL: {template['file_url']}")
            print(f"   Video URL: {template['demo_video_url']}")
            return template
        else:
            print(f"❌ Template creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    finally:
        # Clean up temporary files
        try:
            os.unlink(template_file_path)
            os.unlink(video_file_path)
            print("🧹 Temporary files cleaned up")
        except:
            pass

def list_templates(token):
    """List all templates"""
    print("\n" + "="*50)
    print("📋 LISTING ALL TEMPLATES")
    print("="*50)
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.get(f"{API_BASE}/templates/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        templates = data.get('results', [])
        print(f"✅ Found {data.get('count', 0)} templates total")
        
        for template in templates:
            print(f"\n📄 Template ID: {template['id']}")
            print(f"   Title: {template['title']}")
            print(f"   Service: {template['service_name']}")
            print(f"   Active: {template['isActive']}")
            print(f"   File Size: {template['file_size_mb']} MB")
            print(f"   Created: {template['created_at']}")
        
        return templates
    else:
        print(f"❌ Failed to list templates: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def search_templates(token, search_term="invoice"):
    """Search templates"""
    print("\n" + "="*50)
    print(f"🔍 SEARCHING TEMPLATES FOR '{search_term}'")
    print("="*50)
    
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    params = {'search': search_term}
    response = requests.get(f"{API_BASE}/templates/", headers=headers, params=params)
    
    if response.status_code == 200:
        data = response.json()
        templates = data.get('results', [])
        print(f"✅ Found {len(templates)} templates matching '{search_term}'")
        
        for template in templates:
            print(f"\n📄 {template['title']}")
            print(f"   Service: {template['service_name']}")
            print(f"   Description: {template['description'][:100]}...")
        
        return templates
    else:
        print(f"❌ Search failed: {response.status_code}")
        print(f"Response: {response.text}")
        return []

def main():
    """Main function"""
    print("🚀 Template API Example Script")
    print("="*50)
    
    # Authenticate
    token = authenticate()
    if not token:
        return
    
    # Get available services
    services = get_services(token)
    if not services:
        print("❌ No services available. Please create a service first.")
        return
    
    # Use the first available service
    service_id = services[0]['id']
    print(f"\n📌 Using service: {services[0]['name']} (ID: {service_id})")
    
    # Create template example
    template = create_template_example(token, service_id)
    
    # List all templates
    list_templates(token)
    
    # Search templates
    search_templates(token, "invoice")
    
    print("\n" + "="*50)
    print("✅ EXAMPLE COMPLETED SUCCESSFULLY!")
    print("="*50)
    print("\n📝 What was demonstrated:")
    print("   ✓ JWT Authentication")
    print("   ✓ Service listing")
    print("   ✓ Template creation with file upload")
    print("   ✓ PowerPoint file support")
    print("   ✓ Video file upload")
    print("   ✓ Template listing")
    print("   ✓ Template searching")
    print("\n🔗 Available endpoints:")
    print(f"   • Templates: {API_BASE}/templates/")
    print(f"   • Services: {API_BASE}/services/")

if __name__ == "__main__":
    main()
