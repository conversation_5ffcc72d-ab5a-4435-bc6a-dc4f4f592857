# Generated by Django 5.2 on 2025-07-25 17:11

import django.db.models.deletion
import services.models
import services.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Type',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='Type name', max_length=255, unique=True)),
                ('description', models.TextField(blank=True, help_text='Type description')),
                ('isActive', models.BooleanField(default=True, help_text='Whether the type is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Type',
                'verbose_name_plural': 'Types',
                'db_table': 'services_type',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='Template title', max_length=255)),
                ('description', models.TextField(help_text='Template description')),
                ('file', models.FileField(help_text='Template file (PDF, DOC, DOCX, TXT - max 100MB)', upload_to=services.models.template_file_upload_path, validators=[services.validators.validate_template_file])),
                ('demo_video', models.FileField(blank=True, help_text='Demo video (MP4, AVI, MOV, WMV - max 100MB)', null=True, upload_to=services.models.demo_video_upload_path, validators=[services.validators.validate_demo_video])),
                ('isActive', models.BooleanField(default=True, help_text='Whether the template is active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('service', models.ForeignKey(help_text='Associated service', on_delete=django.db.models.deletion.CASCADE, related_name='templates', to='services.service')),
                ('type', models.ForeignKey(help_text='Template type', on_delete=django.db.models.deletion.CASCADE, related_name='templates', to='services.type')),
            ],
            options={
                'verbose_name': 'Template',
                'verbose_name_plural': 'Templates',
                'db_table': 'services_template',
                'ordering': ['-created_at'],
                'unique_together': {('title', 'type', 'service')},
            },
        ),
    ]
