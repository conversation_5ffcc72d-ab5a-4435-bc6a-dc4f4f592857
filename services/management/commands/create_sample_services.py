from django.core.management.base import BaseCommand
from services.models import Service


class Command(BaseCommand):
    help = 'Create sample services for testing'

    def handle(self, *args, **options):
        """Create sample services"""
        
        sample_services = [
            {
                'name': 'Email Service',
                'isActive': True,
                'toolName': ['sendgrid', 'mailgun', 'ses']
            },
            {
                'name': 'SMS Service',
                'isActive': True,
                'toolName': 'twilio'
            },
            {
                'name': 'Payment Service',
                'isActive': True,
                'toolName': ['stripe', 'paypal', 'square']
            },
            {
                'name': 'Storage Service',
                'isActive': True,
                'toolName': ['aws-s3', 'google-cloud', 'azure-blob']
            },
            {
                'name': 'Analytics Service',
                'isActive': False,
                'toolName': ['google-analytics', 'mixpanel']
            },
            {
                'name': 'Push Notification Service',
                'isActive': True,
                'toolName': 'firebase'
            },
            {
                'name': 'Video Service',
                'isActive': True,
                'toolName': ['youtube-api', 'vimeo-api', 'wistia']
            },
            {
                'name': 'Social Media Service',
                'isActive': False,
                'toolName': ['twitter-api', 'facebook-api', 'instagram-api']
            }
        ]
        
        created_count = 0
        updated_count = 0
        
        for service_data in sample_services:
            service, created = Service.objects.get_or_create(
                name=service_data['name'],
                defaults={
                    'isActive': service_data['isActive'],
                    'toolName': service_data['toolName']
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created service: {service.name}')
                )
            else:
                # Update existing service
                service.isActive = service_data['isActive']
                service.toolName = service_data['toolName']
                service.save()
                updated_count += 1
                self.stdout.write(
                    self.style.WARNING(f'🔄 Updated service: {service.name}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'\n🎉 Sample services creation completed!'
                f'\n📊 Created: {created_count} services'
                f'\n📊 Updated: {updated_count} services'
                f'\n📊 Total: {Service.objects.count()} services in database'
            )
        )
