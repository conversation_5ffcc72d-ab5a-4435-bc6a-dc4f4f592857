from rest_framework import serializers
from .models import Service, Template


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model with full CRUD operations
    """
    tool_names_display = serializers.ReadOnlyField()
    tool_names_list = serializers.ReadOnlyField()
    
    class Meta:
        model = Service
        fields = [
            'id', 'name', 'isActive', 'toolName', 
            'tool_names_display', 'tool_names_list',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'tool_names_display', 'tool_names_list']
    
    def validate_name(self, value):
        """
        Validate service name
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        
        # Check for uniqueness, excluding current instance during updates
        queryset = Service.objects.filter(name__iexact=value.strip())
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("A service with this name already exists")
        
        return value.strip()
    
    def validate_toolName(self, value):
        """
        Validate toolName field
        """
        if not value:
            raise serializers.ValidationError("Tool name(s) cannot be empty")
        
        # Validate based on type
        if isinstance(value, str):
            if not value.strip():
                raise serializers.ValidationError("Tool name cannot be empty")
            return value.strip()
        elif isinstance(value, list):
            if len(value) == 0:
                raise serializers.ValidationError("Tool names list cannot be empty")
            
            # Clean and validate each tool name
            cleaned_tools = []
            for tool in value:
                if not isinstance(tool, str):
                    raise serializers.ValidationError("All tool names must be strings")
                cleaned_tool = tool.strip()
                if not cleaned_tool:
                    raise serializers.ValidationError("Tool names cannot be empty")
                if cleaned_tool not in cleaned_tools:  # Remove duplicates
                    cleaned_tools.append(cleaned_tool)
            
            if len(cleaned_tools) == 0:
                raise serializers.ValidationError("At least one valid tool name is required")
            
            return cleaned_tools
        else:
            raise serializers.ValidationError("Tool names must be either a string or a list of strings")


class ServiceListSerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for listing services
    """
    tool_names_display = serializers.ReadOnlyField()
    
    class Meta:
        model = Service
        fields = ['id', 'name', 'isActive', 'tool_names_display', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at', 'tool_names_display']


class ServiceCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating services
    """
    class Meta:
        model = Service
        fields = ['name', 'isActive', 'toolName']
    
    def validate_name(self, value):
        """
        Validate service name for creation
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        
        if Service.objects.filter(name__iexact=value.strip()).exists():
            raise serializers.ValidationError("A service with this name already exists")
        
        return value.strip()
    
    def validate_toolName(self, value):
        """
        Validate toolName field for creation
        """
        if not value:
            raise serializers.ValidationError("Tool name(s) cannot be empty")
        
        # Validate based on type
        if isinstance(value, str):
            if not value.strip():
                raise serializers.ValidationError("Tool name cannot be empty")
            return value.strip()
        elif isinstance(value, list):
            if len(value) == 0:
                raise serializers.ValidationError("Tool names list cannot be empty")
            
            # Clean and validate each tool name
            cleaned_tools = []
            for tool in value:
                if not isinstance(tool, str):
                    raise serializers.ValidationError("All tool names must be strings")
                cleaned_tool = tool.strip()
                if not cleaned_tool:
                    raise serializers.ValidationError("Tool names cannot be empty")
                if cleaned_tool not in cleaned_tools:  # Remove duplicates
                    cleaned_tools.append(cleaned_tool)
            
            if len(cleaned_tools) == 0:
                raise serializers.ValidationError("At least one valid tool name is required")
            
            return cleaned_tools
        else:
            raise serializers.ValidationError("Tool names must be either a string or a list of strings")


class ServiceUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating services
    """
    class Meta:
        model = Service
        fields = ['name', 'isActive', 'toolName']
    
    def validate_name(self, value):
        """
        Validate service name for updates
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Service name cannot be empty")
        
        # Check for uniqueness, excluding current instance
        queryset = Service.objects.filter(name__iexact=value.strip())
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("A service with this name already exists")
        
        return value.strip()
    
    def validate_toolName(self, value):
        """
        Validate toolName field for updates
        """
        if not value:
            raise serializers.ValidationError("Tool name(s) cannot be empty")
        
        # Validate based on type
        if isinstance(value, str):
            if not value.strip():
                raise serializers.ValidationError("Tool name cannot be empty")
            return value.strip()
        elif isinstance(value, list):
            if len(value) == 0:
                raise serializers.ValidationError("Tool names list cannot be empty")
            
            # Clean and validate each tool name
            cleaned_tools = []
            for tool in value:
                if not isinstance(tool, str):
                    raise serializers.ValidationError("All tool names must be strings")
                cleaned_tool = tool.strip()
                if not cleaned_tool:
                    raise serializers.ValidationError("Tool names cannot be empty")
                if cleaned_tool not in cleaned_tools:  # Remove duplicates
                    cleaned_tools.append(cleaned_tool)
            
            if len(cleaned_tools) == 0:
                raise serializers.ValidationError("At least one valid tool name is required")
            
            return cleaned_tools
        else:
            raise serializers.ValidationError("Tool names must be either a string or a list of strings")


# ============================================================================
# TEMPLATE SERIALIZERS
# ============================================================================

class TemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for Template model with full CRUD operations
    """
    service_name = serializers.CharField(source='service.name', read_only=True)
    file_size_mb = serializers.ReadOnlyField()
    demo_video_size_mb = serializers.ReadOnlyField()
    file_extension = serializers.ReadOnlyField()
    demo_video_extension = serializers.ReadOnlyField()
    file_url = serializers.SerializerMethodField()
    demo_video_url = serializers.SerializerMethodField()

    class Meta:
        model = Template
        fields = [
            'id', 'title', 'description', 'file', 'demo_video',
            'service', 'service_name', 'isActive',
            'file_size_mb', 'demo_video_size_mb', 'file_extension', 'demo_video_extension',
            'file_url', 'demo_video_url', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'service_name',
            'file_size_mb', 'demo_video_size_mb', 'file_extension', 'demo_video_extension',
            'file_url', 'demo_video_url'
        ]

    def get_file_url(self, obj):
        """
        Get full URL for file
        """
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None

    def get_demo_video_url(self, obj):
        """
        Get full URL for demo video
        """
        if obj.demo_video:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.demo_video.url)
            return obj.demo_video.url
        return None


class TemplateListSerializer(serializers.ModelSerializer):
    """
    Serializer for Template list view (minimal fields)
    """
    service_name = serializers.CharField(source='service.name', read_only=True)
    file_size_mb = serializers.ReadOnlyField()
    file_extension = serializers.ReadOnlyField()

    class Meta:
        model = Template
        fields = [
            'id', 'title', 'description', 'service', 'service_name', 'isActive', 'file_size_mb',
            'file_extension', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'service_name',
            'file_size_mb', 'file_extension'
        ]


class TemplateCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating templates
    """
    class Meta:
        model = Template
        fields = ['title', 'description', 'file', 'demo_video', 'service', 'isActive']

    def validate_title(self, value):
        """
        Validate template title for creation
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Template title cannot be empty")

        return value.strip()

    def validate_service(self, value):
        """
        Validate service exists and is active
        """
        if not value.isActive:
            raise serializers.ValidationError("Cannot assign template to inactive service")
        return value

    def validate(self, attrs):
        """
        Validate unique constraint
        """
        title = attrs.get('title')
        service_obj = attrs.get('service')

        if Template.objects.filter(title=title, service=service_obj).exists():
            raise serializers.ValidationError(
                "A template with this title already exists for this service"
            )

        return attrs


class TemplateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating templates
    """
    class Meta:
        model = Template
        fields = ['title', 'description', 'file', 'demo_video', 'service', 'isActive']

    def validate_title(self, value):
        """
        Validate template title for updates
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Template title cannot be empty")

        return value.strip()

    def validate_service(self, value):
        """
        Validate service exists and is active
        """
        if not value.isActive:
            raise serializers.ValidationError("Cannot assign template to inactive service")
        return value

    def validate(self, attrs):
        """
        Validate unique constraint for updates
        """
        title = attrs.get('title')
        service_obj = attrs.get('service')

        if title and service_obj:
            # Check for uniqueness, excluding current instance
            queryset = Template.objects.filter(title=title, service=service_obj)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)

            if queryset.exists():
                raise serializers.ValidationError(
                    "A template with this title already exists for this service"
                )

        return attrs


class TemplateUploadSerializer(serializers.ModelSerializer):
    """
    Specialized serializer for file uploads with enhanced validation
    """
    class Meta:
        model = Template
        fields = ['file', 'demo_video']

    def validate_file(self, value):
        """
        Enhanced file validation
        """
        if value:
            # File size validation (already handled by model validators)
            # Additional custom validation can be added here
            pass
        return value

    def validate_demo_video(self, value):
        """
        Enhanced demo video validation
        """
        if value:
            # Video file validation (already handled by model validators)
            # Additional custom validation can be added here
            pass
        return value
