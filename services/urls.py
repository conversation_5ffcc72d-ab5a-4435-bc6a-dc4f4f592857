from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create a router for all ViewSets
router = DefaultRouter()
router.register(r'services', views.ServiceViewSet, basename='services')
router.register(r'templates', views.TemplateViewSet, basename='templates')

app_name = 'services'

urlpatterns = [
    # Include all router URLs
    path('', include(router.urls)),
]
