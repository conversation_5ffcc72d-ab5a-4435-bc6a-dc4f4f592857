import os
from django.core.exceptions import ValidationError
from django.utils.deconstruct import deconstructible


@deconstructible
class FileSizeValidator:
    """
    Validator to check file size
    """
    def __init__(self, max_size_mb=200):
        self.max_size_mb = max_size_mb
        self.max_size_bytes = max_size_mb * 1024 * 1024

    def __call__(self, value):
        if value.size > self.max_size_bytes:
            raise ValidationError(
                f'File size cannot exceed {self.max_size_mb}MB. '
                f'Current file size: {value.size / (1024 * 1024):.2f}MB'
            )

    def __eq__(self, other):
        return (
            isinstance(other, FileSizeValidator) and
            self.max_size_mb == other.max_size_mb
        )


@deconstructible
class FileTypeValidator:
    """
    Validator to check file type by extension
    """
    def __init__(self, allowed_extensions, message=None):
        self.allowed_extensions = [ext.lower() for ext in allowed_extensions]
        self.message = message or f'File type not allowed. Allowed types: {", ".join(self.allowed_extensions)}'

    def __call__(self, value):
        ext = os.path.splitext(value.name)[1].lower()
        if ext not in self.allowed_extensions:
            raise ValidationError(self.message)

    def __eq__(self, other):
        return (
            isinstance(other, FileTypeValidator) and
            self.allowed_extensions == other.allowed_extensions
        )


# Pre-configured validators for common use cases
def validate_document_file(value):
    """Validate document files (PDF, DOC, DOCX, TXT, PPT, PPTX)"""
    validator = FileTypeValidator(['.pdf', '.doc', '.docx', '.txt', '.ppt', '.pptx'])
    validator(value)


def validate_video_file(value):
    """Validate video files (MP4, AVI, MOV, WMV)"""
    validator = FileTypeValidator(['.mp4', '.avi', '.mov', '.wmv'])
    validator(value)


def validate_image_file(value):
    """Validate image files (JPG, JPEG, PNG, GIF)"""
    validator = FileTypeValidator(['.jpg', '.jpeg', '.png', '.gif'])
    validator(value)


def validate_file_size_200mb(value):
    """Validate file size up to 200MB"""
    validator = FileSizeValidator(max_size_mb=200)
    validator(value)


def validate_file_size_100mb(value):
    """Validate file size up to 100MB"""
    validator = FileSizeValidator(max_size_mb=100)
    validator(value)


def validate_file_size_50mb(value):
    """Validate file size up to 50MB"""
    validator = FileSizeValidator(max_size_mb=50)
    validator(value)


def validate_file_size_10mb(value):
    """Validate file size up to 10MB"""
    validator = FileSizeValidator(max_size_mb=10)
    validator(value)


# Combined validators for specific use cases
def validate_template_file(value):
    """
    Validate template files: documents up to 200MB
    """
    validate_document_file(value)
    validate_file_size_200mb(value)


def validate_demo_video(value):
    """
    Validate demo video files: videos up to 200MB
    """
    validate_video_file(value)
    validate_file_size_200mb(value)


def validate_tool_names(value):
    """
    Validate that toolName is either a string or a list of strings
    (Moved from models.py for reusability)
    """
    if isinstance(value, str):
        return
    elif isinstance(value, list):
        if not all(isinstance(item, str) for item in value):
            raise ValidationError("All tool names must be strings")
        if len(value) == 0:
            raise ValidationError("Tool names list cannot be empty")
    else:
        raise ValidationError("Tool names must be either a string or a list of strings")
