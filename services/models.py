from django.db import models
from django.core.exceptions import ValidationError
import json
from .validators import validate_tool_names, validate_template_file, validate_demo_video


class Service(models.Model):
    """
    Service model for managing services with tool names
    """
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255, unique=True, help_text="Service name")
    isActive = models.BooleanField(default=True, help_text="Whether the service is active")
    toolName = models.JSONField(
        validators=[validate_tool_names],
        help_text="Tool name(s) - can be a single string or list of strings"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'services_service'
        ordering = ['-created_at']
        verbose_name = 'Service'
        verbose_name_plural = 'Services'

    def __str__(self):
        return self.name

    def clean(self):
        """
        Additional validation for the model
        """
        super().clean()
        if self.toolName:
            validate_tool_names(self.toolName)

    def save(self, *args, **kwargs):
        """
        Override save to run full_clean validation
        """
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def tool_names_list(self):
        """
        Return tool names as a list regardless of storage format
        """
        if isinstance(self.toolName, str):
            return [self.toolName]
        return self.toolName or []

    @property
    def tool_names_display(self):
        """
        Return a formatted string of tool names for display
        """
        tool_names = self.tool_names_list
        if len(tool_names) == 1:
            return tool_names[0]
        return ", ".join(tool_names)





def template_file_upload_path(instance, filename):
    """
    Generate upload path for template files
    """
    return f'templates/{filename}'


def demo_video_upload_path(instance, filename):
    """
    Generate upload path for demo videos
    """
    return f'demo_videos/{filename}'


class Template(models.Model):
    """
    Template model with relationship to Service
    """
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=255, help_text="Template title")
    description = models.TextField(help_text="Template description")

    # File fields
    file = models.FileField(
        upload_to=template_file_upload_path,
        validators=[validate_template_file],
        help_text="Template file (PDF, DOC, DOCX, TXT, PPT, PPTX - max 200MB)"
    )
    demo_video = models.FileField(
        upload_to=demo_video_upload_path,
        validators=[validate_demo_video],
        blank=True,
        null=True,
        help_text="Demo video (MP4, AVI, MOV, WMV - max 200MB)"
    )

    # Relationships
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='templates',
        help_text="Associated service"
    )

    # Status
    isActive = models.BooleanField(default=True, help_text="Whether the template is active")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'services_template'
        ordering = ['-created_at']
        verbose_name = 'Template'
        verbose_name_plural = 'Templates'
        unique_together = ['title', 'service']

    def __str__(self):
        return f"{self.title} ({self.service.name})"

    def clean(self):
        """
        Additional validation for the model
        """
        super().clean()
        if self.title:
            self.title = self.title.strip()

    def save(self, *args, **kwargs):
        """
        Override save to run full_clean validation
        """
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def file_size_mb(self):
        """
        Return file size in MB
        """
        if self.file:
            return round(self.file.size / (1024 * 1024), 2)
        return 0

    @property
    def demo_video_size_mb(self):
        """
        Return demo video size in MB
        """
        if self.demo_video:
            return round(self.demo_video.size / (1024 * 1024), 2)
        return 0

    @property
    def file_extension(self):
        """
        Return file extension
        """
        if self.file:
            return self.file.name.split('.')[-1].upper()
        return None

    @property
    def demo_video_extension(self):
        """
        Return demo video extension
        """
        if self.demo_video:
            return self.demo_video.name.split('.')[-1].upper()
        return None
