from django.contrib import admin
from .models import Service, Template


@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'isActive', 'tool_names_display', 'created_at', 'updated_at')
    list_filter = ('isActive', 'created_at', 'updated_at')
    search_fields = ('name', 'toolName')
    ordering = ('-created_at',)
    readonly_fields = ('id', 'created_at', 'updated_at', 'tool_names_display', 'tool_names_list')

    fieldsets = (
        (None, {
            'fields': ('name', 'isActive', 'toolName')
        }),
        ('Tool Names Info', {
            'fields': ('tool_names_display', 'tool_names_list'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """
        Customize queryset for admin
        """
        return super().get_queryset(request)



@admin.register(Template)
class TemplateAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'title', 'service', 'isActive',
        'file_size_display', 'video_size_display', 'created_at', 'updated_at'
    )
    list_filter = ('isActive', 'service', 'created_at', 'updated_at')
    search_fields = ('title', 'description', 'service__name')
    ordering = ('-created_at',)
    readonly_fields = (
        'id', 'created_at', 'updated_at', 'file_size_mb', 'demo_video_size_mb',
        'file_extension', 'demo_video_extension'
    )

    fieldsets = (
        (None, {
            'fields': ('title', 'description', 'service', 'isActive')
        }),
        ('Files', {
            'fields': ('file', 'demo_video')
        }),
        ('File Information', {
            'fields': ('file_size_mb', 'file_extension', 'demo_video_size_mb', 'demo_video_extension'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def file_size_display(self, obj):
        """
        Display file size in a readable format
        """
        if obj.file:
            return f"{obj.file_size_mb} MB"
        return "No file"
    file_size_display.short_description = 'File Size'

    def video_size_display(self, obj):
        """
        Display video size in a readable format
        """
        if obj.demo_video:
            return f"{obj.demo_video_size_mb} MB"
        return "No video"
    video_size_display.short_description = 'Video Size'

    def get_queryset(self, request):
        """
        Optimize queryset with select_related
        """
        return super().get_queryset(request).select_related('service')
