#!/usr/bin/env python3
"""
Basic test script for Type and Template API endpoints
Tests basic CRUD operations without file uploads
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

# Test credentials (Admin user)
ADMIN_CREDENTIALS = {
    "phone": "+1234567890",  # Replace with actual admin phone
    "password": "admin123"   # Replace with actual admin password
}

def authenticate():
    """Authenticate and get JWT token"""
    print("🔐 Authenticating...")
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=ADMIN_CREDENTIALS)
    
    if response.status_code == 200:
        data = response.json()
        token = data.get('access')
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
        print(f"✅ Authentication successful")
        return headers
    else:
        print(f"❌ Authentication failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_type_operations(headers):
    """Test basic Type operations"""
    print("\n" + "="*40)
    print("🧪 TESTING TYPE OPERATIONS")
    print("="*40)
    
    # 1. Create Type
    print("\n1️⃣ Creating Type...")
    type_data = {
        "name": "Basic Test Type",
        "description": "A basic test type",
        "isActive": True
    }
    
    response = requests.post(f"{API_BASE}/types/", json=type_data, headers=headers)
    
    if response.status_code == 201:
        test_type = response.json()
        print(f"✅ Type created: {test_type['name']} (ID: {test_type['id']})")
    else:
        print(f"❌ Type creation failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None
    
    # 2. List Types
    print("\n2️⃣ Listing Types...")
    response = requests.get(f"{API_BASE}/types/", headers=headers)
    
    if response.status_code == 200:
        types_data = response.json()
        print(f"✅ Found {types_data['count']} types")
        for type_item in types_data['results'][:3]:  # Show first 3
            print(f"   - {type_item['name']} (Active: {type_item['isActive']})")
    else:
        print(f"❌ Types listing failed: {response.status_code}")
    
    # 3. Get Type Details
    print("\n3️⃣ Getting Type Details...")
    response = requests.get(f"{API_BASE}/types/{test_type['id']}/", headers=headers)
    
    if response.status_code == 200:
        type_detail = response.json()
        print(f"✅ Type details retrieved: {type_detail['name']}")
        print(f"   Templates count: {type_detail['templates_count']}")
    else:
        print(f"❌ Type details failed: {response.status_code}")
    
    # 4. Update Type
    print("\n4️⃣ Updating Type...")
    update_data = {
        "name": "Updated Basic Test Type",
        "description": "Updated description"
    }
    
    response = requests.patch(f"{API_BASE}/types/{test_type['id']}/", json=update_data, headers=headers)
    
    if response.status_code == 200:
        updated_type = response.json()
        print(f"✅ Type updated: {updated_type['name']}")
    else:
        print(f"❌ Type update failed: {response.status_code}")
    
    # 5. Test Custom Actions
    print("\n5️⃣ Testing Custom Actions...")
    
    # Get active types
    response = requests.get(f"{API_BASE}/types/active/", headers=headers)
    if response.status_code == 200:
        active_types = response.json()
        print(f"✅ Active types: {active_types['count']}")
    
    # Deactivate type
    response = requests.post(f"{API_BASE}/types/{test_type['id']}/deactivate/", headers=headers)
    if response.status_code == 200:
        print("✅ Type deactivated")
    
    # Get inactive types
    response = requests.get(f"{API_BASE}/types/inactive/", headers=headers)
    if response.status_code == 200:
        inactive_types = response.json()
        print(f"✅ Inactive types: {inactive_types['count']}")
    
    return test_type

def get_test_service(headers):
    """Get an existing service for template testing"""
    print("\n🔍 Getting test service...")
    response = requests.get(f"{API_BASE}/services/", headers=headers)
    
    if response.status_code == 200:
        services_data = response.json()
        if services_data['count'] > 0:
            service = services_data['results'][0]
            print(f"✅ Using service: {service['name']} (ID: {service['id']})")
            return service
        else:
            print("⚠️ No services found. Please create a service first.")
            return None
    else:
        print(f"❌ Failed to get services: {response.status_code}")
        return None

def test_template_basic_operations(headers, test_type, test_service):
    """Test basic Template operations (without file uploads)"""
    print("\n" + "="*40)
    print("🧪 TESTING TEMPLATE OPERATIONS")
    print("="*40)
    
    print("ℹ️ Note: This test creates templates without files for basic testing")
    
    # 1. List Templates
    print("\n1️⃣ Listing Templates...")
    response = requests.get(f"{API_BASE}/templates/", headers=headers)
    
    if response.status_code == 200:
        templates_data = response.json()
        print(f"✅ Found {templates_data['count']} templates")
        for template in templates_data['results'][:3]:  # Show first 3
            print(f"   - {template['title']} ({template['type_name']} - {template['service_name']})")
    else:
        print(f"❌ Templates listing failed: {response.status_code}")
    
    # 2. Test Filtering
    print("\n2️⃣ Testing Template Filtering...")
    
    # Filter by type
    response = requests.get(f"{API_BASE}/templates/?type={test_type['id']}", headers=headers)
    if response.status_code == 200:
        filtered_data = response.json()
        print(f"✅ Templates by type: {filtered_data['count']}")
    
    # Filter by service
    response = requests.get(f"{API_BASE}/templates/?service={test_service['id']}", headers=headers)
    if response.status_code == 200:
        filtered_data = response.json()
        print(f"✅ Templates by service: {filtered_data['count']}")
    
    # Search templates
    response = requests.get(f"{API_BASE}/templates/search/?q=template", headers=headers)
    if response.status_code == 200:
        search_data = response.json()
        print(f"✅ Search results: {search_data['count']}")
    
    # 3. Test Custom Actions
    print("\n3️⃣ Testing Custom Actions...")
    
    # Active templates
    response = requests.get(f"{API_BASE}/templates/active/", headers=headers)
    if response.status_code == 200:
        active_templates = response.json()
        print(f"✅ Active templates: {active_templates['count']}")
    
    # Templates by type endpoint
    response = requests.get(f"{API_BASE}/templates/by-type/{test_type['id']}/", headers=headers)
    if response.status_code == 200:
        type_templates = response.json()
        print(f"✅ Templates by type endpoint: {type_templates['count']}")
    
    # Templates by service endpoint
    response = requests.get(f"{API_BASE}/templates/by-service/{test_service['id']}/", headers=headers)
    if response.status_code == 200:
        service_templates = response.json()
        print(f"✅ Templates by service endpoint: {service_templates['count']}")

def test_api_endpoints():
    """Test all API endpoints"""
    print("🚀 Starting Basic Type and Template API Tests")
    print("="*50)
    
    # Authenticate
    headers = authenticate()
    if not headers:
        print("❌ Authentication failed. Cannot proceed with tests.")
        return False
    
    try:
        # Test Type operations
        test_type = test_type_operations(headers)
        if not test_type:
            print("❌ Type operations failed")
            return False
        
        # Get test service
        test_service = get_test_service(headers)
        if not test_service:
            print("❌ No test service available")
            return False
        
        # Test Template operations
        test_template_basic_operations(headers, test_type, test_service)
        
        print("\n" + "="*50)
        print("🎉 BASIC TESTS COMPLETED!")
        print("="*50)
        print("✅ Type CRUD operations working")
        print("✅ Template filtering and search working")
        print("✅ Custom actions working")
        print("\nℹ️ For file upload testing, use test_type_template_api.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with exception: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_api_endpoints()
    
    if success:
        print("\n✅ Basic tests passed! The APIs are working correctly.")
        print("💡 Next steps:")
        print("   1. Test file uploads with test_type_template_api.py")
        print("   2. Create some sample data")
        print("   3. Test the admin interface")
    else:
        print("\n❌ Some tests failed. Please check the output above for details.")
        exit(1)
